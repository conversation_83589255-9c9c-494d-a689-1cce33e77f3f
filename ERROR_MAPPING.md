# Psychiatry App - Comprehensive Error Mapping & Debugging Guide

## 🔍 **Critical Error Analysis**

### **1. Primary 404 Error: Assessment Sessions Endpoint**
**Error**: `GET /api/patients/assessment-sessions:1 Failed to load resource: the server responded with a status of 404 (Not Found)`

**Root Cause Analysis**:
- **Frontend Issue**: The frontend is calling `/api/patients/assessment-sessions` (missing patient ID)
- **Correct Endpoint**: Should be `/api/patients/{patientId}/assessment-sessions`
- **Location**: [`frontend/src/features/history-taking/services/historyTakingService.ts:43`](frontend/src/features/history-taking/services/historyTakingService.ts:43)

**Cascading Impact**:
- Assessment sessions cannot be retrieved by ID
- History-taking feature fails to load existing sessions
- Patient psychiatric assessments become inaccessible

### **2. Frontend Error Boundary Analysis**

**Error Boundary Coverage**:
- **Component**: [`ErrorBoundary.tsx`](frontend/src/components/ErrorBoundary.tsx)
- **Scope**: Global React error handling
- **Limitations**: Only catches React component errors, not API failures

**Failure Points Identified**:
1. **API Response Handling**: No error boundaries for async operations
2. **Undefined Property Access**: Common in analytics components
3. **Missing Null Checks**: Throughout data display components

### **3. Backend API Error Patterns**

#### **Authentication & Authorization Failures**
- **401 Unauthorized**: Missing/invalid JWT tokens
- **403 Forbidden**: Insufficient role permissions
- **Routes Affected**: All `/api/*` endpoints

#### **Database Constraint Violations**
- **Foreign Key Failures**: 
  - `providerId` references in appointments
  - `patientId` references across all tables
- **Unique Constraint Violations**: 
  - `patientId` field in patients table
  - `username` and `email` in users table

#### **Data Validation Errors**
- **Date Parsing**: Invalid date formats in appointments
- **Required Fields**: Missing mandatory parameters
- **JSON Parsing**: Malformed JSON in request bodies

### **4. Database Schema Vulnerabilities**

#### **Critical Missing Columns (Fixed)**
- ✅ `appointments.providerId` - Added via migration
- ✅ `patients.isDeleted` - Added for soft delete functionality
- ✅ Column naming consistency (`isDeleted` vs `is_deleted`)

#### **Potential Cascade Failures**
1. **Patient Deletion Impact**:
   - All related appointments, lab results, assessments
   - Foreign key cascade rules in place

2. **User Deletion Impact**:
   - Created patients, appointments, audit logs
   - Soft delete implemented to prevent data loss

### **5. Frontend Service Error Patterns**

#### **API Service Issues**
- **Response Structure Mismatch**: Frontend expects `response.data.data`, backend sometimes returns `response.data`
- **Error Handling**: Generic catch blocks without specific error types
- **Loading States**: Missing proper loading indicators

#### **Type Safety Gaps**
- **Optional Chaining Missing**: Direct property access on potentially undefined objects
- **Default Values**: Not provided for optional API response fields

## 🎯 **Error Cross-Reference & Cascading Impact Analysis**

### **Error Cascade Chain #1: Assessment System**
```
Frontend 404 → Patient Not Found → Assessment Creation Fails → Clinical Data Loss
```
**Impact**: Complete assessment workflow failure

### **Error Cascade Chain #2: Analytics Dashboard**
```
Missing Data → Undefined Properties → Component Crash → Blank Dashboard
```
**Impact**: Loss of clinical insights and reporting

### **Error Cascade Chain #3: Appointment System**
```
Invalid Date → Database Constraint → Appointment Creation Fails → Scheduling Disruption
```
**Impact**: Patient appointment management failure

## 📋 **Prioritized Inspection Checklist**

### **🔴 CRITICAL (Immediate Fix Required)**
- [ ] Fix assessment session endpoint calls in frontend
- [ ] Add proper patient ID parameter to API calls
- [ ] Implement comprehensive API error handling

### **🟡 HIGH (Next Priority)**
- [ ] Add loading states to all async operations
- [ ] Implement proper error boundaries for API failures
- [ ] Add input validation for all forms
- [ ] Standardize API response formats

### **🟢 MEDIUM (Improvement)**
- [ ] Add retry logic for failed API calls
- [ ] Implement proper logging for debugging
- [ ] Add user-friendly error messages
- [ ] Create fallback UI components

### **🔵 LOW (Enhancement)**
- [ ] Add performance monitoring
- [ ] Implement error reporting service
- [ ] Add analytics for error tracking
- [ ] Create automated error alerts

## 🔍 **Searchable Error Documentation**

### **Error Codes & Messages**
| Error Code | Message | Cause | Solution |
|------------|---------|-------|----------|
| 404 | `Cannot GET /api/patients/assessment-sessions` | Missing patient ID in URL | Add patientId parameter |
| 500 | `Foreign key constraint failed` | Invalid providerId/patientId | Validate references before creation |
| 400 | `Invalid date format` | Malformed date string | Implement robust date parsing |
| 401 | `Unauthorized` | Missing/invalid JWT | Check authentication flow |
| 403 | `Forbidden` | Insufficient permissions | Verify user roles |

### **Common Stack Traces**
```
TypeError: Cannot read properties of undefined (reading 'total')
Location: AnalyticsPage component
Fix: Add optional chaining (?.) and default values

PrismaClientKnownRequestError: 
Invalid `prisma.appointment.create()` invocation
Fix: Validate foreign key references before creation
```

### **Database Error Patterns**
- **SQLite Error 19**: Foreign key constraint violation
- **SQLite Error 1299**: NOT NULL constraint failed
- **Prisma Error P2002**: Unique constraint violation
- **Prisma Error P2003**: Foreign key constraint failed

## 🛠️ **Quick Debugging Commands**

### **Database Verification**
```bash
# Check table structure
sqlite3 backend/database/dev.db ".schema appointments"

# Verify foreign key relationships
sqlite3 backend/database/dev.db "PRAGMA foreign_key_list(appointments);"

# Check for orphaned records
sqlite3 backend/database/dev.db "SELECT * FROM appointments WHERE providerId NOT IN (SELECT id FROM users);"
```

### **API Testing**
```bash
# Test assessment sessions endpoint
curl -X GET http://localhost:3002/api/patients/P12346/assessment-sessions \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# Test with missing patient ID (should fail)
curl -X GET http://localhost:3002/api/patients/assessment-sessions \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### **Frontend Debugging**
```javascript
// Add to browser console for debugging
console.log('API Base URL:', import.meta.env.VITE_API_URL);
console.log('Current assessment session ID:', sessionId);
console.log('Patient ID from URL:', patientId);
```

## 🚨 **Immediate Action Required**

**Primary Issue**: The frontend is calling `/api/patients/assessment-sessions` instead of `/api/patients/{patientId}/assessment-sessions`

**Fix Location**: [`historyTakingService.ts:43`](frontend/src/features/history-taking/services/historyTakingService.ts:43)

**Solution**: Update the getAssessmentSessionById method to include the patient ID parameter or use the correct endpoint structure.