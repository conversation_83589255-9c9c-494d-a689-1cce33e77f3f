const axios = require('axios');

async function testAppointmentCreation() {
  try {
    console.log('Testing appointment creation...');
    
    // First, let's get a list of patients to use a valid patient ID
    const patientsResponse = await axios.get('http://localhost:3002/api/patients');
    console.log('Patients response:', patientsResponse.data);
    
    if (!patientsResponse.data.data || patientsResponse.data.data.length === 0) {
      console.log('No patients found. Creating a test patient first...');
      
      // Create a test patient
      const patientData = {
        firstName: 'Test',
        lastName: 'Patient',
        dateOfBirth: '1990-01-01',
        gender: 'MALE',
        phone: '555-0123',
        email: '<EMAIL>'
      };
      
      const createPatientResponse = await axios.post('http://localhost:3002/api/patients', patientData);
      console.log('Created patient:', createPatientResponse.data);
    }
    
    // Get patients again
    const updatedPatientsResponse = await axios.get('http://localhost:3002/api/patients');
    const patients = updatedPatientsResponse.data.data;
    
    if (patients && patients.length > 0) {
      const testPatient = patients[0];
      console.log('Using patient:', testPatient);
      
      // Get a valid user ID for provider
      const usersResponse = await axios.get('http://localhost:3002/api/users');
      const users = usersResponse.data.data;
      const validProvider = users.find(user => user.role === 'CLINICIAN') || users[0];

      // Test appointment creation
      const appointmentData = {
        patientId: testPatient.id,
        providerId: validProvider.id,
        date: '2024-07-20T10:00:00.000Z',
        duration: 30,
        type: 'CONSULTATION',
        status: 'SCHEDULED',
        notes: 'Test appointment'
      };
      
      console.log('Creating appointment with data:', appointmentData);
      
      const appointmentResponse = await axios.post('http://localhost:3002/api/appointments', appointmentData);
      console.log('Appointment created successfully:', appointmentResponse.data);
    }
    
  } catch (error) {
    console.error('Error testing appointment creation:');
    console.error('Status:', error.response?.status);
    console.error('Data:', error.response?.data);
    console.error('Message:', error.message);
    if (error.response?.data?.error) {
      console.error('Detailed error:', error.response.data.error);
    }
  }
}

testAppointmentCreation();
