const axios = require('axios');

async function testAppointmentWithRealUser() {
  try {
    console.log('Testing appointment creation with real user ID...');
    
    // Get patients
    const patientsResponse = await axios.get('http://localhost:3002/api/patients');
    const patients = patientsResponse.data.data;
    
    if (patients && patients.length > 0) {
      const testPatient = patients[0];
      console.log('Using patient:', testPatient.patientId, testPatient.firstName, testPatient.lastName);
      
      // Test appointment creation with the real admin user ID that the frontend now uses
      const appointmentData = {
        patientId: testPatient.id,
        providerId: 'ab002f5f-c207-49ef-8dcf-83ae0fc7f2da', // Real admin user ID
        date: '2024-07-21T14:00:00.000Z',
        duration: 60,
        type: 'INITIAL_CONSULTATION',
        status: 'SCHEDULED',
        notes: 'Test appointment with real user ID'
      };
      
      console.log('Creating appointment with data:', appointmentData);
      
      const appointmentResponse = await axios.post('http://localhost:3002/api/appointments', appointmentData);
      console.log('✅ Appointment created successfully!');
      console.log('Appointment ID:', appointmentResponse.data.data.id);
      console.log('Patient:', appointmentResponse.data.data.patient.firstName, appointmentResponse.data.data.patient.lastName);
      console.log('Date:', appointmentResponse.data.data.date);
      console.log('Type:', appointmentResponse.data.data.type);
    }
    
  } catch (error) {
    console.error('❌ Error testing appointment creation:');
    console.error('Status:', error.response?.status);
    console.error('Data:', error.response?.data);
    console.error('Message:', error.message);
  }
}

testAppointmentWithRealUser();
