const axios = require('axios');

async function testDashboardAnalytics() {
  try {
    console.log('Testing dashboard analytics...');
    
    // Test basic data counts first
    console.log('\n🔍 Testing basic data counts...');
    const [patientsResp, appointmentsResp, usersResp] = await Promise.all([
      axios.get('http://localhost:3002/api/patients'),
      axios.get('http://localhost:3002/api/appointments'),
      axios.get('http://localhost:3002/api/users')
    ]);
    
    console.log('✅ Basic data counts:');
    console.log('  - Patients:', patientsResp.data.count);
    console.log('  - Appointments:', appointmentsResp.data.count);
    console.log('  - Users:', usersResp.data.count);
    
    // Test dashboard analytics endpoint
    console.log('\n📊 Testing dashboard analytics endpoint...');
    const dashboardResponse = await axios.get('http://localhost:3002/api/analytics/dashboard');
    
    console.log('✅ Dashboard analytics response:');
    console.log('  - Success:', dashboardResponse.data.success);
    console.log('  - Data structure:', Object.keys(dashboardResponse.data.data));
    
    if (dashboardResponse.data.data.analytics) {
      const analytics = dashboardResponse.data.data.analytics;
      console.log('\n📈 Analytics values:');
      console.log('  - Patients total:', analytics.patients?.total || 'undefined');
      console.log('  - Appointments total:', analytics.appointments?.total || 'undefined');
      console.log('  - Lab results total:', analytics.labResults?.total || 'undefined');
      console.log('  - Users total:', analytics.system?.totalUsers || 'undefined');
      
      // Compare with basic counts
      if (analytics.patients?.total !== patientsResp.data.count) {
        console.log('\n⚠️ MISMATCH: Analytics patients count does not match API count!');
        console.log('  - Analytics:', analytics.patients?.total);
        console.log('  - API:', patientsResp.data.count);
      }
      
      if (analytics.appointments?.total !== appointmentsResp.data.count) {
        console.log('\n⚠️ MISMATCH: Analytics appointments count does not match API count!');
        console.log('  - Analytics:', analytics.appointments?.total);
        console.log('  - API:', appointmentsResp.data.count);
      }
    }
    
  } catch (error) {
    console.error('❌ Error testing dashboard analytics:');
    console.error('Status:', error.response?.status);
    console.error('Data:', error.response?.data);
    console.error('Message:', error.message);
  }
}

testDashboardAnalytics();
