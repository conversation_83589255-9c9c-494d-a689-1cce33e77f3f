const axios = require('axios');

async function testLabResults() {
  try {
    console.log('🧪 Testing Lab Results endpoint...\n');

    // Login to get authentication token
    console.log('1️⃣ Logging in...');
    const loginResponse = await axios.post('http://localhost:3002/api/auth/login', {
      username: 'dr.smith',
      password: 'clinician123!'
    });

    if (!loginResponse.data.success) {
      console.log('❌ Login failed:', loginResponse.data.message);
      return;
    }

    console.log('✅ Login successful');
    const token = loginResponse.data.data.accessToken;
    const headers = {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };

    // Test lab results creation
    console.log('\n2️⃣ Testing lab results creation...');
    const labResultData = {
      patientId: 'P12345', // Using the human-readable ID
      testType: 'Complete Blood Count',
      testDate: new Date().toISOString(),
      orderedBy: '<PERSON><PERSON> <PERSON>',
      labName: 'Springfield Lab',
      results: {
        'WBC': '7.2',
        'RBC': '4.5',
        'Hemoglobin': '14.2',
        'Hematocrit': '42.1'
      },
      normalRanges: {
        'WBC': '4.0-11.0',
        'RBC': '4.2-5.4',
        'Hemoglobin': '12.0-16.0',
        'Hematocrit': '36.0-46.0'
      },
      flags: {},
      notes: 'Normal results',
      status: 'COMPLETED'
    };

    try {
      const labResponse = await axios.post(
        'http://localhost:3002/api/lab-results',
        labResultData,
        { headers }
      );

      if (labResponse.data.success) {
        console.log('✅ Lab result created successfully!');
        console.log('   Lab Result ID:', labResponse.data.data.id);
        console.log('   Test Type:', labResponse.data.data.testType);
        console.log('   Patient:', labResponse.data.data.patient.firstName, labResponse.data.data.patient.lastName);
      } else {
        console.log('❌ Lab result creation failed:', labResponse.data.message);
      }
    } catch (labError) {
      console.log('❌ Lab result creation error:', labError.response?.data?.error || labError.message);
      console.log('   Status:', labError.response?.status);
      console.log('   Full error:', labError.response?.data);
    }

  } catch (error) {
    console.error('❌ Test error:', error.response?.data || error.message);
  }
}

testLabResults();
