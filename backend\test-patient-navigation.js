const axios = require('axios');

async function testPatientNavigation() {
  try {
    console.log('Testing patient navigation and assessment flow...');
    
    // Get a patient to test with
    const patientsResponse = await axios.get('http://localhost:3002/api/patients');
    const patients = patientsResponse.data.data;
    
    if (patients && patients.length > 0) {
      const testPatient = patients[0];
      console.log('✅ Patient found:');
      console.log('  - ID:', testPatient.id);
      console.log('  - Patient ID:', testPatient.patientId);
      console.log('  - Name:', testPatient.firstName, testPatient.lastName);
      
      // Test assessment session creation
      console.log('\n📝 Testing assessment session creation...');
      const assessmentData = {
        sessionDate: new Date().toISOString(),
        assessments: [
          {
            disorderId: 'major-depressive-disorder',
            patientId: testPatient.id,
            criteria: [
              {
                id: 'depressed-mood',
                code: 'A1',
                description: 'Depressed mood most of the day',
                present: true,
                severity: 3,
                duration: '2 weeks',
                comments: 'Patient reports feeling sad most days'
              }
            ],
            assessmentDate: new Date().toISOString(),
            status: 'completed'
          }
        ],
        clinicalImpression: 'Patient shows signs of depression',
        riskAssessment: {
          suicidalIdeation: false,
          homicidalIdeation: false,
          selfHarm: false,
          substanceUse: false,
          level: 'low'
        },
        status: 'completed',
        duration: 60
      };
      
      const assessmentResponse = await axios.post(
        `http://localhost:3002/api/patients/${testPatient.id}/assessment-sessions`,
        assessmentData
      );
      
      console.log('✅ Assessment session created successfully!');
      console.log('  - Session ID:', assessmentResponse.data.data.id);
      
      // Test getting the assessment session back
      console.log('\n📋 Testing assessment session retrieval...');
      const sessionId = assessmentResponse.data.data.id;
      const getSessionResponse = await axios.get(`http://localhost:3002/api/assessment-sessions/${sessionId}`);
      
      console.log('✅ Assessment session retrieved successfully!');
      console.log('  - Session ID:', getSessionResponse.data.data.id);
      console.log('  - Patient ID:', getSessionResponse.data.data.patientId);
      console.log('  - Status:', getSessionResponse.data.data.status);
      
      // Test getting patient's assessment sessions
      console.log('\n📚 Testing patient assessment sessions list...');
      const patientSessionsResponse = await axios.get(`http://localhost:3002/api/patients/${testPatient.id}/assessment-sessions`);
      
      console.log('✅ Patient assessment sessions retrieved successfully!');
      console.log('  - Number of sessions:', patientSessionsResponse.data.data.length);
      
      console.log('\n🎉 All patient navigation tests passed!');
      console.log('\nNavigation URLs that should work:');
      console.log(`  - Patient Details: /patients/${testPatient.id}`);
      console.log(`  - Patient Edit: /patients/${testPatient.id}/edit`);
      console.log(`  - History Taking: /history-taking/${testPatient.id}`);
      
    } else {
      console.log('❌ No patients found in database');
    }
    
  } catch (error) {
    console.error('❌ Error testing patient navigation:');
    console.error('Status:', error.response?.status);
    console.error('Data:', error.response?.data);
    console.error('Message:', error.message);
  }
}

testPatientNavigation();
