const axios = require('axios');

async function testStaffCreation() {
  try {
    console.log('Testing staff user creation...');
    
    // Test creating a new staff member
    const staffData = {
      username: 'test.staff',
      email: '<EMAIL>',
      password: 'TestPassword123!',
      firstName: 'Test',
      lastName: 'Staff',
      role: 'STAFF'
    };
    
    console.log('Creating staff member with data:', {
      ...staffData,
      password: '[HIDDEN]'
    });
    
    const response = await axios.post('http://localhost:3002/api/users', staffData);
    
    console.log('✅ Staff member created successfully!');
    console.log('  - ID:', response.data.data.id);
    console.log('  - Username:', response.data.data.username);
    console.log('  - Email:', response.data.data.email);
    console.log('  - Role:', response.data.data.role);
    console.log('  - Name:', response.data.data.firstName, response.data.data.lastName);
    
    // Test getting all users to verify the new staff member is there
    console.log('\n📋 Testing user list retrieval...');
    const usersResponse = await axios.get('http://localhost:3002/api/users');
    
    console.log('✅ Users retrieved successfully!');
    console.log('  - Total users:', usersResponse.data.count);
    
    const newUser = usersResponse.data.data.find(user => user.username === staffData.username);
    if (newUser) {
      console.log('  - New staff member found in list:', newUser.firstName, newUser.lastName);
    } else {
      console.log('  - ❌ New staff member not found in list');
    }
    
  } catch (error) {
    console.error('❌ Error testing staff creation:');
    console.error('Status:', error.response?.status);
    console.error('Data:', error.response?.data);
    console.error('Message:', error.message);
    
    if (error.response?.status === 409) {
      console.log('\n💡 This might be expected if the user already exists. Let\'s try with a different username...');
      
      // Try with a unique username
      const uniqueStaffData = {
        username: `test.staff.${Date.now()}`,
        email: `test.staff.${Date.now()}@example.com`,
        password: 'TestPassword123!',
        firstName: 'Test',
        lastName: 'Staff',
        role: 'STAFF'
      };
      
      try {
        const retryResponse = await axios.post('http://localhost:3002/api/users', uniqueStaffData);
        console.log('✅ Staff member created with unique username!');
        console.log('  - Username:', retryResponse.data.data.username);
        console.log('  - Email:', retryResponse.data.data.email);
      } catch (retryError) {
        console.error('❌ Still failed with unique username:', retryError.response?.data || retryError.message);
      }
    }
  }
}

testStaffCreation();
