console.log('Testing staff creation...');

const axios = require('axios');

async function testStaff() {
  try {
    const response = await axios.get('http://localhost:3002/api/users');
    console.log('Current users:', response.data.count);
    
    const staffData = {
      username: `staff${Date.now()}`,
      email: `staff${Date.now()}@example.com`,
      password: 'Password123!',
      firstName: 'Test',
      lastName: 'Staff',
      role: 'STAFF'
    };
    
    const createResponse = await axios.post('http://localhost:3002/api/users', staffData);
    console.log('Staff created:', createResponse.data.data.username);
    
  } catch (error) {
    console.error('Error:', error.response?.data || error.message);
  }
}

testStaff();
