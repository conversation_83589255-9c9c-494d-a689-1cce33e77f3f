const axios = require('axios');

async function testUsers() {
  try {
    console.log('Testing users endpoint...');
    
    // Try to get users
    const usersResponse = await axios.get('http://localhost:3002/api/users');
    console.log('Users response:', usersResponse.data);
    
  } catch (error) {
    console.error('Error testing users:');
    console.error('Status:', error.response?.status);
    console.error('Data:', error.response?.data);
    console.error('Message:', error.message);
  }
}

testUsers();
