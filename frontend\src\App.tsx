import React, { Suspense, lazy } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { AuthProvider } from './features/auth';
import { ProtectedRoute } from './features/auth';
import Layout from './components/layout/Layout';
import LoadingSpinner from './components/ui/LoadingSpinner';
import QueryProvider from './providers/QueryProvider';
import { ToastProvider } from './components/ui/Toast';
import { ErrorBoundary } from './components/ErrorBoundary';

// Lazy load feature components
const LoginPage = lazy(() => import('./features/auth/components/LoginPage'));
const DashboardPage = lazy(() => import('./features/dashboard/components/DashboardPage'));
const PatientsPage = lazy(() => import('./features/patients/components/PatientsPage'));
const AddPatientPage = lazy(() => import('./features/patients/components/AddPatientPage'));
const PatientDetailsPage = lazy(() => import('./features/patients/components/PatientDetailsPage'));
const AppointmentsPage = lazy(() => import('./features/appointments/components/AppointmentsPage'));
const AddAppointmentPage = lazy(() => import('./features/appointments/components/AddAppointmentPage'));
const LabResultsPage = lazy(() => import('./features/lab-results/components/LabResultsPage'));
const AddLabResultPage = lazy(() => import('./features/lab-results/components/AddLabResultPage'));
const LabManagementPage = lazy(() => import('./components/LabManagementPage'));
const UsersPage = lazy(() => import('./features/users/components/UsersPage'));
const AddUserPage = lazy(() => import('./features/users/components/AddUserPage'));
const AnalyticsPage = lazy(() => import('./features/analytics/components/AnalyticsPage'));
const PatientSelectionPage = lazy(() => import('./features/history-taking/components/PatientSelectionPage'));
const HistoryTakingPage = lazy(() => import('./features/history-taking/components/HistoryTakingPage'));

// Main App Component
const App: React.FC = () => {
  return (
    <ErrorBoundary>
      <QueryProvider>
        <ToastProvider>
          <AuthProvider>
            <Router>
              <Suspense fallback={<LoadingSpinner />}>
                <Routes>
            {/* Public routes */}
            <Route path="/login" element={<LoginPage />} />

            {/* Protected routes */}
            <Route path="/*" element={
              <ProtectedRoute>
                <Layout>
                  <Routes>
                    <Route path="/" element={<DashboardPage />} />

                    {/* Patient routes */}
                    <Route path="/patients" element={
                      <ProtectedRoute requiredPermission="view_patients">
                        <PatientsPage />
                      </ProtectedRoute>
                    } />
                    <Route path="/patients/new" element={
                      <ProtectedRoute requiredPermission="manage_patients">
                        <AddPatientPage />
                      </ProtectedRoute>
                    } />
                    <Route path="/patients/:patientId" element={
                      <ProtectedRoute requiredPermission="view_patients">
                        <PatientDetailsPage />
                      </ProtectedRoute>
                    } />

                    {/* Appointment routes */}
                    <Route path="/appointments" element={
                      <ProtectedRoute requiredPermission="view_appointments">
                        <AppointmentsPage />
                      </ProtectedRoute>
                    } />
                    <Route path="/appointments/new" element={
                      <ProtectedRoute requiredPermission="manage_appointments">
                        <AddAppointmentPage />
                      </ProtectedRoute>
                    } />

                    {/* Lab result routes */}
                    <Route path="/lab-results" element={
                      <ProtectedRoute requiredPermission="view_lab_results">
                        <LabResultsPage />
                      </ProtectedRoute>
                    } />
                    <Route path="/lab-results/new" element={
                      <ProtectedRoute requiredPermission="manage_lab_results">
                        <AddLabResultPage />
                      </ProtectedRoute>
                    } />
                    <Route path="/lab-management" element={
                      <ProtectedRoute requiredPermission="manage_lab_results">
                        <LabManagementPage />
                      </ProtectedRoute>
                    } />

                    {/* User management routes */}
                    <Route path="/users" element={
                      <ProtectedRoute requiredPermission="manage_users">
                        <UsersPage />
                      </ProtectedRoute>
                    } />
                    <Route path="/users/new" element={
                      <ProtectedRoute requiredPermission="manage_users">
                        <AddUserPage />
                      </ProtectedRoute>
                    } />

                    {/* Analytics route */}
                    <Route path="/analytics" element={
                      <ProtectedRoute requiredPermission="view_analytics">
                        <AnalyticsPage />
                      </ProtectedRoute>
                    } />

                    {/* History Taking routes */}
                    <Route path="/history-taking" element={
                      <ProtectedRoute requiredPermission="manage_patients">
                        <PatientSelectionPage />
                      </ProtectedRoute>
                    } />
                    <Route path="/history-taking/:patientId" element={
                      <ProtectedRoute requiredPermission="manage_patients">
                        <HistoryTakingPage />
                      </ProtectedRoute>
                    } />
                  </Routes>
                </Layout>
              </ProtectedRoute>
            } />
                </Routes>
              </Suspense>
            </Router>
          </AuthProvider>
        </ToastProvider>
      </QueryProvider>
    </ErrorBoundary>
  );
};

export default App;